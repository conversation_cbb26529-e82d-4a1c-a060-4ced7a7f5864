import { Server, ServerCredentials, type ServerUnaryCall, type ServerWritableStream } from '@grpc/grpc-js'
import { type CompiledInstruction, type LegacyMessage, type MessageHeader, type PingRequest, type PongResponse, ShredForwarderServiceService, type SubscribeRequest, type SubscribeResponse, type VersionedTransaction } from '../../src/clients/shred-forwarder/generated/shred_forwarder'

export class MockShredForwarderServer {
    private readonly server: Server
    private isRunning = false
    private readonly subscriptionStreams = new Set<ServerWritableStream<SubscribeRequest, SubscribeResponse>>()
    private intervalId?: NodeJS.Timeout

    constructor(private readonly port = 50_051) {
        this.server = new Server()
        this.setupService()
    }

    private setupService() {
        this.server.addService(ShredForwarderServiceService, {
            ping: this.handlePing.bind(this),
            subscribe: this.handleSubscribe.bind(this),
        })
    }

    private handlePing(call: ServerUnary<PERSON>all<PingRequest, PongResponse>, callback: (error: any, response: PongResponse) => void) {
        const request = call.request
        console.log(`[Server] Received ping request: count=${request.count}`)

        const response: PongResponse = {
            count: request.count,
            timestamp: new Date(),
            version: '1.0.0-test',
        }

        console.log(`[Server] Sending pong response: count=${response.count}, version=${response.version}`)
        callback(null, response)
    }

    private handleSubscribe(call: ServerWritableStream<SubscribeRequest, SubscribeResponse>) {
        console.log('[Server] New subscription started')
        this.subscriptionStreams.add(call)

        call.on('cancelled', () => {
            console.log('[Server] Subscription cancelled by client')
            this.subscriptionStreams.delete(call)
        })

        call.on('error', (error) => {
            console.log('[Server] Subscription error:', error.message)
            this.subscriptionStreams.delete(call)
        })

        // Start sending mock data
        this.startSendingMockData()
    }

    private startSendingMockData() {
        if (this.intervalId) { return }

        let slot = 1000n
        let entryIndex = 0
        let transactionIndex = 0

        this.intervalId = setInterval(() => {
            if (this.subscriptionStreams.size === 0) { return }

            const mockTransaction: VersionedTransaction = {
                signatures: [Buffer.from(`mock_signature_${Date.now()}`, 'utf8')],
                message: {
                    legacy: {
                        header: {
                            numRequiredSignatures: 1,
                            numReadonlySignedAccounts: 0,
                            numReadonlyUnsignedAccounts: 1,
                        } as MessageHeader,
                        accountKeys: [
                            Buffer.from('11111111111111111111111111111111', 'hex'),
                            Buffer.from('22222222222222222222222222222222', 'hex'),
                        ],
                        recentBlockhash: Buffer.from(`mock_blockhash_${Date.now()}`, 'utf8'),
                        instructions: [{
                            programIdIndex: 0,
                            accounts: [1],
                            data: Buffer.from('mock_instruction_data', 'utf8'),
                        } as CompiledInstruction],
                    } as LegacyMessage,
                },
            }

            const response: SubscribeResponse = {
                slot: slot++,
                entryIndex: entryIndex++,
                transactionIndex: transactionIndex++,
                transaction: mockTransaction,
                receivedAt: new Date(),
                processingTimeNanos: BigInt(Math.floor(Math.random() * 1_000_000)),
            }

            console.log(`[Server] Sending mock data: slot=${response.slot}, entryIndex=${response.entryIndex}`)

            // Send to all active streams
            for (const stream of this.subscriptionStreams) {
                try {
                    stream.write(response)
                } catch (error) {
                    console.log('[Server] Error writing to stream:', error)
                    this.subscriptionStreams.delete(stream)
                }
            }
        }, 1000) // Send data every second
    }

    public async start(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.server.bindAsync(
                `0.0.0.0:${this.port}`,
                ServerCredentials.createInsecure(),
                (error, port) => {
                    if (error) {
                        reject(error)

                        return
                    }

                    this.isRunning = true
                    console.log(`[Server] Mock ShredForwarder server started on port ${port}`)
                    resolve()
                },
            )
        })
    }

    public async stop(): Promise<void> {
        return new Promise((resolve) => {
            if (this.intervalId) {
                clearInterval(this.intervalId)
                this.intervalId = undefined
            }

            // Close all active streams
            for (const stream of this.subscriptionStreams) {
                try {
                    stream.end()
                } catch (error) {
                    console.log('[Server] Error closing stream:', error)
                }
            }

            this.subscriptionStreams.clear()

            this.server.tryShutdown((error) => {
                if (error) {
                    console.log('[Server] Error during shutdown:', error)
                }

                this.isRunning = false
                console.log('[Server] Mock server stopped')
                resolve()
            })
        })
    }

    public forceDisconnectClients(): void {
        console.log('[Server] Force disconnecting all clients')

        for (const stream of this.subscriptionStreams) {
            try {
                stream.destroy()
            } catch (error) {
                console.log('[Server] Error destroying stream:', error)
            }
        }

        this.subscriptionStreams.clear()
    }

    public get activeConnections(): number {
        return this.subscriptionStreams.size
    }

    public get running(): boolean {
        return this.isRunning
    }
}

// Example usage - check if this file is being run directly
const isMainModule = import.meta.url === `file://${process.argv[1]}`

if (isMainModule) {
    const server = new MockShredForwarderServer()

    const startServer = async () => {
        try {
            await server.start()

            // Simulate force disconnect after 10 seconds for testing
            setTimeout(() => {
                console.log('[Server] Simulating force disconnect...')
                server.forceDisconnectClients()
            }, 10_000)
        } catch (error) {
            console.error('[Server] Failed to start:', error)
        }
    }

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log('[Server] Received SIGINT, shutting down...')
        await server.stop()
        process.exit(0)
    })

    startServer()
}
