import { ShredForwarderClient } from '../../src/clients/shred-forwarder/client'
import { MockShredForwarderServer } from './server'

class ShredForwarderClientTest {
    private client: ShredForwarderClient
    private server: MockShredForwarderServer
    private readonly serverPort = 50051
    private readonly serverUrl = `localhost:${this.serverPort}`

    constructor() {
        this.server = new MockShredForwarderServer(this.serverPort)
        this.client = new ShredForwarderClient(this.serverUrl)
    }

    public async runTests(): Promise<void> {
        console.log('=== ShredForwarder Client Test Suite ===\n')

        try {
            // Start mock server
            await this.startServer()

            // Run test cases
            await this.testPingRequest()
            await this.testStreamingSubscription()
            await this.testReconnectionLogic()

            console.log('\n=== All tests completed successfully ===')
        } catch (error) {
            console.error('\n=== Test failed ===')
            console.error('Error:', error)
        } finally {
            await this.cleanup()
        }
    }

    private async startServer(): Promise<void> {
        console.log('[Test] Starting mock server...')
        await this.server.start()
        // Wait a bit for server to be ready
        await this.sleep(1000)
        console.log('[Test] Mock server ready\n')
    }

    private async testPingRequest(): Promise<void> {
        console.log('--- Test 1: Ping Request ---')
        
        try {
            const pingRequest = { count: 42 }
            console.log(`[Client] Sending ping request: count=${pingRequest.count}`)
            
            const response = await this.client.ping(pingRequest)
            
            console.log(`[Client] Received pong response:`)
            console.log(`  - count: ${response.count}`)
            console.log(`  - timestamp: ${response.timestamp}`)
            console.log(`  - version: ${response.version}`)
            
            if (response.count === pingRequest.count) {
                console.log('[Test] ✅ Ping test passed')
            } else {
                throw new Error(`Expected count ${pingRequest.count}, got ${response.count}`)
            }
        } catch (error) {
            console.log('[Test] ❌ Ping test failed:', error)
            throw error
        }
        
        console.log()
    }

    private async testStreamingSubscription(): Promise<void> {
        console.log('--- Test 2: Streaming Subscription ---')
        
        return new Promise((resolve, reject) => {
            let messageCount = 0
            const maxMessages = 5
            
            try {
                const stream = this.client.createStream({
                    resubscribe: false // Disable auto-resubscribe for this test
                })

                stream.on('data', (data) => {
                    messageCount++
                    console.log(`[Client] Received message ${messageCount}:`)
                    console.log(`  - slot: ${data.slot}`)
                    console.log(`  - entryIndex: ${data.entryIndex}`)
                    console.log(`  - transactionIndex: ${data.transactionIndex}`)
                    console.log(`  - receivedAt: ${data.receivedAt}`)
                    console.log(`  - processingTimeNanos: ${data.processingTimeNanos}`)
                    
                    if (data.transaction) {
                        console.log(`  - transaction signatures: ${data.transaction.signatures.length}`)
                        if (data.transaction.message?.legacy) {
                            console.log(`  - legacy message with ${data.transaction.message.legacy.instructions.length} instructions`)
                        }
                    }
                    
                    if (messageCount >= maxMessages) {
                        console.log(`[Test] ✅ Streaming test passed - received ${messageCount} messages`)
                        stream.close()
                        resolve()
                    }
                })

                stream.on('error', (error) => {
                    console.log('[Client] Stream error:', error.message)
                    reject(error)
                })

                stream.on('closed', (isExplicitly, error) => {
                    console.log(`[Client] Stream closed - explicitly: ${isExplicitly}`)
                    if (error) {
                        console.log('[Client] Close error:', error)
                    }
                })

                stream.on('state', (state) => {
                    console.log(`[Client] Stream state changed: ${state}`)
                })

                console.log('[Client] Starting subscription...')
                stream.subscribe()
                
            } catch (error) {
                console.log('[Test] ❌ Streaming test failed:', error)
                reject(error)
            }
        })
    }

    private async testReconnectionLogic(): Promise<void> {
        console.log('\n--- Test 3: Reconnection Logic ---')
        
        return new Promise((resolve, reject) => {
            let messageCount = 0
            let reconnectCount = 0
            const maxReconnects = 2
            
            try {
                const stream = this.client.createStream({
                    resubscribe: {
                        enabled: true,
                        maxRetries: 5,
                        initialDelay: 1000,
                        maxDelay: 5000
                    }
                })

                stream.on('data', (data) => {
                    messageCount++
                    console.log(`[Client] Received message ${messageCount} (slot: ${data.slot})`)
                })

                stream.on('resubscribeStarted', (reason, attempt) => {
                    reconnectCount++
                    console.log(`[Client] Reconnection attempt ${attempt} started (reason: ${reason})`)
                    
                    if (reconnectCount >= maxReconnects) {
                        console.log(`[Test] ✅ Reconnection test passed - ${reconnectCount} reconnections detected`)
                        stream.close()
                        resolve()
                    }
                })

                stream.on('resubscribeSucceeded', (attempt) => {
                    console.log(`[Client] Reconnection attempt ${attempt} succeeded`)
                })

                stream.on('resubscribeFailed', (reason, attempt, error) => {
                    console.log(`[Client] Reconnection attempt ${attempt} failed (reason: ${reason}):`, error?.message)
                })

                stream.on('error', (error) => {
                    console.log('[Client] Stream error:', error.message)
                })

                stream.on('closed', (isExplicitly, error) => {
                    console.log(`[Client] Stream closed - explicitly: ${isExplicitly}`)
                    if (error && !isExplicitly) {
                        console.log('[Client] Unexpected close, should trigger reconnection')
                    }
                })

                console.log('[Client] Starting subscription with reconnection enabled...')
                stream.subscribe()

                // Force disconnect after 3 seconds to trigger reconnection
                setTimeout(() => {
                    console.log('[Test] Triggering first force disconnect...')
                    this.server.forceDisconnectClients()
                }, 3000)

                // Force disconnect again after 8 seconds
                setTimeout(() => {
                    console.log('[Test] Triggering second force disconnect...')
                    this.server.forceDisconnectClients()
                }, 8000)

                // Timeout the test after 20 seconds
                setTimeout(() => {
                    console.log('[Test] ❌ Reconnection test timeout')
                    stream.close()
                    reject(new Error('Reconnection test timeout'))
                }, 20000)
                
            } catch (error) {
                console.log('[Test] ❌ Reconnection test failed:', error)
                reject(error)
            }
        })
    }

    private async cleanup(): Promise<void> {
        console.log('\n[Test] Cleaning up...')
        try {
            await this.server.stop()
            console.log('[Test] Cleanup completed')
        } catch (error) {
            console.log('[Test] Cleanup error:', error)
        }
    }

    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms))
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const test = new ShredForwarderClientTest()
    test.runTests().catch(console.error)
}

export { ShredForwarderClientTest }
